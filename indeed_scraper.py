import time
import logging
import random
from typing import List, Dict, Optional
from fastapi import FastAPI, Query, Body
from fastapi.responses import JSONResponse
import uvicorn
import re
import json
import requests
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Country code to Indeed domain mapping
INDEED_DOMAINS = {
    "us": "https://www.indeed.com",
    "uk": "https://www.indeed.co.uk",
    "in": "https://www.indeed.co.in",
    "ca": "https://ca.indeed.com",
    "au": "https://au.indeed.com",
    "fr": "https://fr.indeed.com",
    "de": "https://de.indeed.com",
    "it": "https://it.indeed.com",
    "es": "https://www.indeed.es",
    "nl": "https://nl.indeed.com",
    "br": "https://www.indeed.com.br",
    "jp": "https://jp.indeed.com",
    "sg": "https://sg.indeed.com",
    "mx": "https://www.indeed.com.mx",
    # Add more as needed
}

def get_indeed_base_url(country: str) -> str:
    return INDEED_DOMAINS.get(country.lower(), INDEED_DOMAINS["us"])

def build_search_url(job_title, location, country):
    base_url = get_indeed_base_url(country)
    params = []
    if job_title:
        params.append(f"q={requests.utils.quote(job_title)}")
    if location:
        params.append(f"l={requests.utils.quote(location)}")
    param_str = "&".join(params)
    return f"{base_url}/jobs?{param_str}" if param_str else f"{base_url}/jobs"

# --- FlareSolverr integration ---
def flaresolverr_get(url, session=None, max_timeout=60000):
    """
    Use FlareSolverr to bypass Cloudflare and fetch the page HTML, cookies, and headers.
    """
    flaresolverr_url = "http://localhost:8191/v1"
    payload = {
        "cmd": "request.get",
        "url": url,
        "maxTimeout": max_timeout
    }
    if session:
        payload["session"] = session
    try:
        resp = requests.post(flaresolverr_url, json=payload, timeout=(max_timeout/1000)+10)
        resp.raise_for_status()
        data = resp.json()
        if data.get("status") == "ok":
            solution = data["solution"]
            return {
                "html": solution["response"],
                "cookies": solution.get("cookies", []),
                "headers": solution.get("headers", {}),
                "user_agent": solution.get("userAgent", None),
                "url": solution.get("url", url)
            }
        else:
            raise Exception(f"FlareSolverr error: {data.get('message')}")
    except Exception as e:
        logger.error(f"FlareSolverr request failed: {e}")
        return None

def random_delay(min_sec=1, max_sec=3):
    delay = random.uniform(min_sec, max_sec)
    time.sleep(delay)

def collect_job_urls_from_html(html, base_url, num_jobs) -> List[str]:
    soup = BeautifulSoup(html, "html.parser")
    job_urls = set()
    for a in soup.select("a.jcs-JobTitle"):
        href = a.get("href")
        if href:
            if href.startswith("/"):
                href = base_url + href
            job_urls.add(href)
            if len(job_urls) >= num_jobs:
                break
    return list(job_urls)

def scrape_job_detail_from_html(html, url) -> Dict:
    soup = BeautifulSoup(html, "html.parser")
    job_data = {
        "job_id": None,
        "title": None,
        "company": None,
        "location": None,
        "salary": None,
        "job_type": None,
        "schedule": None,
        "benefits": None,
        "job_description": None,
        "jd_url": url
    }
    # Extract job ID from URL
    job_id_match = re.search(r"jk=([a-zA-Z0-9]+)", url)
    if job_id_match:
        job_data["job_id"] = job_id_match.group(1)
    # Title
    title = soup.select_one("h1[data-testid='jobsearch-JobInfoHeader-title'], h1.jobsearch-JobInfoHeader-title, .jobsearch-JobInfoHeader-title, h1")
    if title:
        job_data["title"] = title.get_text(strip=True)
    # Company
    company = soup.select_one("[data-testid='inlineHeader-companyName'], .jobsearch-InlineCompanyRating-companyHeader a, .jobsearch-InlineCompanyRating-companyHeader, [data-testid='jobsearch-CompanyInfoWithoutHeaderImage'] a")
    if company:
        job_data["company"] = company.get_text(strip=True)
    # Location
    location = soup.select_one("[data-testid='jobsearch-JobInfoHeader-companyLocation'], .jobsearch-JobInfoHeader-subtitle, [data-testid='job-location']")
    if location:
        job_data["location"] = location.get_text(strip=True)
    # Salary
    salary = None
    for sel in ["#salaryInfoAndJobType span", "[data-testid='jobsearch-JobMetadataHeader-item'] span", ".jobsearch-JobMetadataHeader-item span"]:
        el = soup.select_one(sel)
        if el and any(k in el.get_text() for k in [',', '€', '£', '₹', 'year', 'hour', 'month', 'per', 'annum', 'week', 'day']):
            salary = el.get_text(strip=True)
            break
    job_data["salary"] = salary
    # Job Type
    job_type = None
    for el in soup.select("[data-testid='jobsearch-JobMetadataHeader-item']"):
        text = el.get_text(strip=True).lower()
        if any(k in text for k in ['full-time', 'part-time', 'contract', 'temporary', 'internship']):
            job_type = el.get_text(strip=True)
            break
    job_data["job_type"] = job_type
    # Schedule
    schedule = None
    for h3 in soup.find_all("h3"):
        if "Shift and schedule" in h3.get_text():
            sibling = h3.find_next_sibling("div")
            if sibling:
                span = sibling.find("span")
                if span:
                    schedule = span.get_text(strip=True)
                    break
    job_data["schedule"] = schedule
    # Benefits
    benefits = None
    ul = soup.select_one("#benefits ul")
    if ul:
        benefits = ul.get_text(strip=True)
    else:
        li_list = soup.select("[data-testid='jobsearch-benefits'] li")
        if li_list:
            benefits = "; ".join([li.get_text(strip=True) for li in li_list if li.get_text(strip=True)])
    job_data["benefits"] = benefits
    # Job Description
    desc = soup.select_one("#jobDescriptionText, [data-testid='jobsearch-jobDescriptionText'], .jobsearch-jobDescriptionText, .jobDescriptionContent")
    if desc:
        job_data["job_description"] = desc.get_text(strip=True)
    return job_data

def indeed_scrape(job_title, location, num_jobs, country, headless=True, manual_cookies=None):
    base_url = get_indeed_base_url(country)
    jobs = []
    try:
        logger.info(f"Starting Indeed scrape for '{job_title}' in '{location}' ({country}) using FlareSolverr")
        search_url = build_search_url(job_title, location, country)
        logger.info(f"Fetching search page: {search_url}")
        result = flaresolverr_get(search_url)
        if not result:
            logger.error("Failed to fetch search page via FlareSolverr")
            return []
        html = result["html"]
        job_urls = collect_job_urls_from_html(html, base_url, num_jobs)
        if not job_urls:
            logger.warning("No job URLs collected")
            return []
        for i, url in enumerate(job_urls):
            logger.info(f"Fetching job {i+1}/{len(job_urls)}: {url}")
            job_result = flaresolverr_get(url)
            if not job_result:
                logger.error(f"Failed to fetch job detail page: {url}")
                continue
            job_html = job_result["html"]
            job_data = scrape_job_detail_from_html(job_html, url)
            jobs.append(job_data)
            if i < len(job_urls) - 1:
                random_delay(2, 5)
        logger.info(f"Successfully scraped {len(jobs)} jobs")
        return jobs
    except Exception as e:
        logger.error(f"Indeed scraping failed: {e}")
        raise

# FastAPI app
app = FastAPI(title="Enhanced Indeed Scraper (FlareSolverr)", version="3.0")

@app.get("/")
def root():
    return {"message": "Indeed Scraper API (FlareSolverr)", "version": "3.0"}

@app.post("/scrape_indeed")
def scrape_indeed_api(
    payload: dict = Body(..., example={
        "job_title": "Software Engineer",
        "location": "New York",
        "num_jobs": 2,
        "country": "us",
        "headless": True
    })
):
    try:
        job_title = payload.get("job_title")
        location = payload.get("location")
        num_jobs = payload.get("num_jobs", 5)
        country = payload.get("country", "us")
        headless = payload.get("headless", True)
        logger.info(f"API request: {job_title} in {location}, {num_jobs} jobs, country: {country}")
        jobs = indeed_scrape(job_title, location, num_jobs, country, headless)
        successful_jobs = [job for job in jobs if job]
        response_data = {
            "status": "success",
            "scraped_jobs": successful_jobs,
            "requested_jobs": num_jobs,
            "successful_jobs": len(successful_jobs),
            "country": country,
            "search_params": {
                "job_title": job_title,
                "location": location,
                "country": country
            }
        }
        return JSONResponse(content=response_data)
    except Exception as e:
        logger.error(f"API error: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": str(e),
                "scraped_jobs": [],
                "requested_jobs": payload.get("num_jobs", 5),
                "successful_jobs": 0,
                "country": payload.get("country", "us")
            }
        )

@app.get("/health")
def health_check():
    return {"status": "healthy", "service": "indeed-scraper-flare"}

@app.get("/supported_countries")
def get_supported_countries():
    return {
        "supported_countries": list(INDEED_DOMAINS.keys()),
        "domains": INDEED_DOMAINS
    }

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger.info("Starting Indeed Scraper (FlareSolverr)...")
    logger.info(f"Supported countries: {list(INDEED_DOMAINS.keys())}")
    uvicorn.run(
        "indeed_scraper:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )