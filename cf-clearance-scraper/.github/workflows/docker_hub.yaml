name: Publish Docker Image

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '20.x'

      - name: Install dependencies
        run: npm install

      - name: Get current version from package.json
        id: get_version
        run: echo "VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_ENV

      - name: Get latest version from Docker Hub
        id: get_docker_version
        run: |
          LATEST_VERSION=$(curl -s "https://hub.docker.com/v2/repositories/zfcsoftware/cf-clearance-scraper/tags?page_size=1" | jq -r '.results[0].name')
          echo "DOCKER_VERSION=$LATEST_VERSION" >> $GITHUB_ENV

      - name: Compare versions
        id: compare_versions
        run: |
          if [ "${{ env.VERSION }}" != "${{ env.DOCKER_VERSION }}" ]; then
            echo "Versions are different. Proceeding to build and publish."
            echo "publish=true" >> $GITHUB_ENV
          else
            echo "Versions are the same. Skipping publish."
            echo "publish=false" >> $GITHUB_ENV
          fi

      - name: Build and Push Docker Image
        if: env.publish == 'true'
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
        run: |
          echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
          docker build -t zfcsoftware/cf-clearance-scraper:$VERSION .
          docker tag zfcsoftware/cf-clearance-scraper:${{ env.VERSION }} zfcsoftware/cf-clearance-scraper:latest
          docker push zfcsoftware/cf-clearance-scraper:latest
          docker push zfcsoftware/cf-clearance-scraper:$VERSION
      - name: Logout from Docker Hub
        run: docker logout
