{"name": "cf-clearance-scraper", "version": "2.1.3", "main": "index.js", "scripts": {"start": "node src/index.js", "test": "node --experimental-vm-modules ./node_modules/.bin/jest --detectOpenHandles --verbose"}, "jest": {"testMatch": ["**/tests/**/*.js"], "verbose": true}, "keywords": ["cf-clearance", "cloudflare", "waf", "scraper", "puppeteer", "xvfb", "turnstile", "bypass", "undetected", "stealth"], "author": "zfcsoftware", "license": "ISC", "description": "This package is an experimental and educational package created for Cloudflare protections.", "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "jest": "^29.7.0", "puppeteer-real-browser": "^1.4.0", "supertest": "^7.0.0"}}