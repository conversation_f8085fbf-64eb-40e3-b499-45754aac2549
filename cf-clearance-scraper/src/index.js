const express = require('express')
const app = express()
const port = process.env.PORT || 3000
const bodyParser = require('body-parser')
const authToken = process.env.authToken || null
const cors = require('cors')
const reqValidate = require('./module/reqValidate')

global.browserLength = 0
global.browserLimit = Number(process.env.browserLimit) || 20
global.timeOut = Number(process.env.timeOut || 60000)

app.use(bodyParser.json({}))
app.use(bodyParser.urlencoded({ extended: true }))
app.use(cors())
if (process.env.NODE_ENV !== 'development') {
    let server = app.listen(port, () => { console.log(`Server running on port ${port}`) })
    try {
        server.timeout = global.timeOut
    } catch (e) { }
}
if (process.env.SKIP_LAUNCH != 'true') require('./module/createBrowser')

const getSource = require('./endpoints/getSource')
const solveTurnstileMin = require('./endpoints/solveTurnstile.min')
const solveTurnstileMax = require('./endpoints/solveTurnstile.max')
const wafSession = require('./endpoints/wafSession')


app.post('/cf-clearance-scraper', async (req, res) => {
    const data = req.body
    const check = reqValidate(data)
    if (check !== true) return res.status(400).json({ code: 400, message: 'Bad Request', schema: check })
    if (authToken && data.authToken !== authToken) return res.status(401).json({ code: 401, message: 'Unauthorized' })
    if (global.browserLength >= global.browserLimit) return res.status(429).json({ code: 429, message: 'Too Many Requests' })
    if (process.env.SKIP_LAUNCH != 'true' && !global.browser) return res.status(500).json({ code: 500, message: 'The scanner is not ready yet. Please try again a little later.' })
    var result = { code: 500 }
    global.browserLength++
    try {
        switch (data.mode) {
            case "source":
                result = await getSource(data)
                    .then(res => ({ source: res, code: 200 }))
                    .catch(err => ({ code: 500, message: err && err.message ? err.message : (typeof err === 'string' ? err : JSON.stringify(err)) }));
                break;
            case "turnstile-min":
                result = await solveTurnstileMin(data)
                    .then(res => ({ token: res, code: 200 }))
                    .catch(err => ({ code: 500, message: err && err.message ? err.message : (typeof err === 'string' ? err : JSON.stringify(err)) }));
                break;
            case "turnstile-max":
                result = await solveTurnstileMax(data)
                    .then(res => ({ token: res, code: 200 }))
                    .catch(err => ({ code: 500, message: err && err.message ? err.message : (typeof err === 'string' ? err : JSON.stringify(err)) }));
                break;
            case "waf-session":
                result = await wafSession(data)
                    .then(res => ({ ...res, code: 200 }))
                    .catch(err => ({ code: 500, message: err && err.message ? err.message : (typeof err === 'string' ? err : JSON.stringify(err)) }));
                break;
            default:
                result = { code: 400, message: 'Invalid mode' };
        }
    } catch (err) {
        result = { code: 500, message: err && err.message ? err.message : (typeof err === 'string' ? err : JSON.stringify(err)) };
    }
    global.browserLength--;
    res.status(result.code ?? 500).json(result);
})

app.use((req, res) => { res.status(404).json({ code: 404, message: 'Not Found' }) })

if (process.env.NODE_ENV == 'development') module.exports = app
