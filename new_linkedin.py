import sys
import time
import json
import re
import logging
import random
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from bs4 import BeautifulSoup
import uvicorn
import undetected_chromedriver as uc

# -------------------- CONFIGURATION --------------------
@dataclass
class LinkedInConfig:
    chrome_driver_path: str = './chromedriver'
    max_workers: int = 4
    default_timeout: int = 20
    page_load_timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 2
    max_pages: int = 10
    selectors: Dict[str, List[str]] = field(default_factory=lambda: {
        'search_input': [
            "input.jobs-search-box__text-input[aria-label='Search by title, skill, or company']",
            "input[aria-label='Search by title, skill, or company']"
        ],
        'location_input': [
            "input.jobs-search-box__text-input[aria-label='City, state, or zip code']",
            "input[aria-label='City, state, or zip code']"
        ],
        'search_button': [
            "button.jobs-search-box__submit-button",
            "button[aria-label='Search']"
        ],
        'job_cards': [
            "li.semantic-search-results-list__list-item",
            "li.job-card-container"
        ],
        'job_links': [
            "a.job-card-job-posting-card-wrapper__card-link",
            "a[data-test-app-aware-link]"
        ],
        'next_button': [
            "button.jobs-search-pagination__button--next",
            "button[aria-label='View next page']"
        ],
        'job_title': [
            "h1.t-24.job-details-jobs-unified-top-card__job-title",
            "h1.t-24.t-bold.inline",
            "h2.t-16.t-black.t-bold.truncate"
        ],
        'company_name': [
            ".job-details-jobs-unified-top-card__company-name a",
            ".artdeco-entity-lockup__title a",
            ".artdeco-entity-lockup__title"
        ],
        'location': [
            ".job-details-jobs-unified-top-card__primary-description-container span.tvm__text--low-emphasis",
            ".artdeco-entity-lockup__caption",
            ".job-card-job-posting-card-wrapper__caption"
        ],
        'salary': [
            "#SALARY .jobs-details__salary-main-rail-card",
            ".jobs-details__salary-main-rail-card"
        ],
        'job_type': [
            ".job-details-fit-level-preferences button",
            ".job-details-fit-level-preferences .artdeco-button--secondary"
        ],
        'work_location': [
            ".job-details-fit-level-preferences button",
            ".job-details-fit-level-preferences .artdeco-button--secondary"
        ],
        'benefits': [
            ".job-details-module .jobs-description__container ul",
            ".jobs-description__container li"
        ],
        'schedule': [
            ".job-details-fit-level-preferences button",
        ],
        'education': [
            ".jobs-details-premium-insight.applicant-education li",
        ],
        'skills': [
            ".jobs-description__container ul",
            ".jobs-description__container li"
        ],
        'job_description': [
            "div.jobs-description-content__text--stretch",
            "article.jobs-description__container"
        ],
        'company_logo': [
            ".artdeco-entity-lockup__image img",
            "img[alt*='logo']"
        ],
        'easy_apply': [
            "button.jobs-apply-button",
            "button[aria-label^='Apply to']"
        ],
    })
    user_agents: List[str] = field(default_factory=lambda: [
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ])

# -------------------- PYDANTIC MODELS --------------------
class LinkedInJob(BaseModel):
    job_id: Optional[str] = None
    title: Optional[str] = None
    company_name: Optional[str] = None
    location: Optional[str] = None
    salary: Optional[str] = None
    job_type: Optional[str] = None
    work_location: Optional[str] = None
    benefits: Optional[Union[str, List[str]]] = None
    schedule: Optional[str] = None
    education: Optional[str] = None
    skills: Optional[List[str]] = None
    job_description: Optional[str] = None
    company_logo: Optional[str] = None
    easy_apply: Optional[bool] = None
    jd_url: Optional[str] = None
    extra_fields: Optional[Dict[str, Any]] = None

class LinkedInScrapeRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = Field(5, ge=1, le=50)

class LinkedInScrapeResponse(BaseModel):
    scraped_jobs: List[LinkedInJob]
    metadata: Dict[str, Any]

# -------------------- LOGGER --------------------
def get_logger(name: str = "LinkedInScraper"):
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    return logger

logger = get_logger()

# -------------------- DRIVER MANAGER --------------------
class DriverManager:
    def __init__(self, config: LinkedInConfig):
        self.config = config
        self.user_agent_cycle = iter(config.user_agents)

    def create_driver(self) -> uc.Chrome:
        options = uc.ChromeOptions()
        # options.add_argument('--headless=new')  # Run in non-headless mode for debugging
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-images')
        options.add_argument(f'--user-agent={next(self.user_agent_cycle, self.config.user_agents[0])}')
        try:
            driver = uc.Chrome(options=options)
            driver.set_page_load_timeout(self.config.page_load_timeout)
            driver.implicitly_wait(5)
            logger.info("Chrome driver created successfully")
            return driver
        except Exception as e:
            logger.error(f"Failed to create driver: {e}")
            raise

# -------------------- FIELD EXTRACTOR --------------------
class FieldExtractor:
    def __init__(self, config: LinkedInConfig):
        self.config = config

    def safe_extract(self, driver, selectors: List[str], attr: Optional[str] = None, multiple: bool = False) -> Any:
        for selector in selectors:
            try:
                if multiple:
                    elements = driver.find_elements('css selector', selector)
                    if elements:
                        if attr:
                            return [e.get_attribute(attr) for e in elements if e.get_attribute(attr)]
                        else:
                            return [e.text.strip() for e in elements if e.text.strip()]
                else:
                    element = driver.find_element('css selector', selector)
                    if attr:
                        return element.get_attribute(attr)
                    else:
                        return element.text.strip()
            except Exception:
                continue
        return None

    def safe_extract_soup(self, soup, selectors: List[str], attr: Optional[str] = None, multiple: bool = False) -> Any:
        for selector in selectors:
            try:
                if multiple:
                    elements = soup.select(selector)
                    if elements:
                        if attr:
                            return [e.get(attr) for e in elements if e.get(attr)]
                        else:
                            return [e.get_text(strip=True) for e in elements if e.get_text(strip=True)]
                else:
                    element = soup.select_one(selector)
                    if element:
                        if attr:
                            return element.get(attr)
                        else:
                            return element.get_text(strip=True)
            except Exception:
                continue
        return None

# -------------------- SMART RETRY --------------------
def smart_retry(func, retries=3, delay=2):
    for attempt in range(retries):
        try:
            return func()
        except Exception as e:
            if attempt == retries - 1:
                logger.error(f"Failed after {retries} attempts: {e}")
                return None
            logger.warning(f"Attempt {attempt+1} failed: {e}, retrying in {delay}s")
            time.sleep(delay)
    return None

# -------------------- SCRAPER CORE --------------------
class LinkedInScraper:
    def __init__(self, config: LinkedInConfig = None):
        self.config = config or LinkedInConfig()
        self.driver_manager = DriverManager(self.config)
        self.extractor = FieldExtractor(self.config)

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict[str, Any]:
        start_time = datetime.utcnow()
        driver = self.driver_manager.create_driver()
        scraped_jobs = []
        errors = []
        try:
            self._perform_search(driver, job_title, location)
            job_urls = self._collect_job_urls(driver, num_jobs)
            logger.info(f"Collected {len(job_urls)} job URLs")
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                results = list(executor.map(lambda url: self._extract_single_job(url), job_urls[:num_jobs]))
            scraped_jobs = [job for job in results if job]
        except Exception as e:
            logger.error(f"Scraping failed: {e}")
            errors.append(str(e))
        finally:
            driver.quit()
        end_time = datetime.utcnow()
        return {
            "scraped_jobs": [job.dict() for job in scraped_jobs],
            "metadata": {
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "execution_time": (end_time - start_time).total_seconds(),
                "requested_jobs": num_jobs,
                "scraped_jobs": len(scraped_jobs),
                "errors": errors
            }
        }

    def _perform_search(self, driver, job_title, location):
        logger.info(f"Navigating to LinkedIn Jobs search page")
        driver.get("https://www.linkedin.com/jobs/")
        time.sleep(random.uniform(2, 4))
        # Fill in job title
        for selector in self.config.selectors['search_input']:
            try:
                elem = driver.find_element('css selector', selector)
                elem.clear()
                elem.send_keys(job_title)
                break
            except Exception:
                continue
        # Fill in location
        for selector in self.config.selectors['location_input']:
            try:
                elem = driver.find_element('css selector', selector)
                elem.clear()
                elem.send_keys(location)
                break
            except Exception:
                continue
        # Click search
        for selector in self.config.selectors['search_button']:
            try:
                btn = driver.find_element('css selector', selector)
                btn.click()
                break
            except Exception:
                continue
        time.sleep(random.uniform(2, 4))

    def _collect_job_urls(self, driver, num_jobs: int) -> List[str]:
        job_urls = set()
        page = 1
        while len(job_urls) < num_jobs and page <= self.config.max_pages:
            logger.info(f"Collecting job URLs from page {page}")
            time.sleep(random.uniform(2, 4))
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            for selector in self.config.selectors['job_links']:
                links = soup.select(selector)
                for link in links:
                    href = link.get('href')
                    if href and href.startswith('http'):
                        job_urls.add(href)
            if len(job_urls) >= num_jobs:
                break
            # Pagination
            next_btn = None
            for selector in self.config.selectors['next_button']:
                try:
                    next_btn = driver.find_element('css selector', selector)
                    if next_btn and next_btn.is_enabled() and next_btn.is_displayed():
                        next_btn.click()
                        page += 1
                        time.sleep(random.uniform(2, 4))
                        break
                except Exception:
                    continue
            else:
                logger.info("No more pages or next button not found.")
                break
        return list(job_urls)

    def _extract_single_job(self, job_url: str) -> Optional[LinkedInJob]:
        driver = self.driver_manager.create_driver()
        try:
            driver.get(job_url)
            time.sleep(random.uniform(2, 4))
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            job_id = self._extract_job_id(job_url)
            title = self.extractor.safe_extract_soup(soup, self.config.selectors['job_title'])
            company_name = self.extractor.safe_extract_soup(soup, self.config.selectors['company_name'])
            location = self.extractor.safe_extract_soup(soup, self.config.selectors['location'])
            salary = self.extractor.safe_extract_soup(soup, self.config.selectors['salary'])
            job_type = self.extractor.safe_extract_soup(soup, self.config.selectors['job_type'])
            work_location = self.extractor.safe_extract_soup(soup, self.config.selectors['work_location'])
            benefits = self.extractor.safe_extract_soup(soup, self.config.selectors['benefits'], multiple=True)
            schedule = self.extractor.safe_extract_soup(soup, self.config.selectors['schedule'])
            education = self.extractor.safe_extract_soup(soup, self.config.selectors['education'])
            skills = self.extractor.safe_extract_soup(soup, self.config.selectors['skills'], multiple=True)
            job_description = self.extractor.safe_extract_soup(soup, self.config.selectors['job_description'])
            company_logo = self.extractor.safe_extract_soup(soup, self.config.selectors['company_logo'], attr='src')
            easy_apply = bool(self.extractor.safe_extract_soup(soup, self.config.selectors['easy_apply']))
            return LinkedInJob(
                job_id=job_id,
                title=title,
                company_name=company_name,
                location=location,
                salary=salary,
                job_type=job_type,
                work_location=work_location,
                benefits=benefits,
                schedule=schedule,
                education=education,
                skills=skills,
                job_description=job_description,
                company_logo=company_logo,
                easy_apply=easy_apply,
                jd_url=job_url
            )
        except Exception as e:
            logger.error(f"Failed to extract job from {job_url}: {e}")
            return None
        finally:
            driver.quit()

    def _extract_job_id(self, url: str) -> Optional[str]:
        match = re.search(r'currentJobId=(\d+)', url)
        if match:
            return match.group(1)
        match = re.search(r'/jobs/view/(\d+)', url)
        if match:
            return match.group(1)
        match = re.search(r'jl=(\d+)', url)
        if match:
            return match.group(1)
        return None

# -------------------- FASTAPI APP --------------------
app = FastAPI(title="LinkedIn Job Scraper", version="1.0")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

scraper = LinkedInScraper()

@app.get("/scrape_jobs", response_model=LinkedInScrapeResponse)
def scrape_jobs_api(job_title: str = Query(...), location: str = Query(...), num_jobs: int = Query(5, ge=1, le=50)):
    result = scraper.scrape_jobs(job_title, location, num_jobs)
    return result

@app.post("/scrape_jobs", response_model=LinkedInScrapeResponse)
def scrape_jobs_post_api(request: LinkedInScrapeRequest):
    result = scraper.scrape_jobs(request.job_title, request.location, request.num_jobs)
    return result

@app.options("/scrape_jobs")
async def options_scrape_jobs():
    return JSONResponse(status_code=200, content={})

@app.get("/health")
def health_check():
    return {"status": "ok"}

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="LinkedIn Job Scraper CLI")
    parser.add_argument('--job_title', type=str, required=True)
    parser.add_argument('--location', type=str, required=True)
    parser.add_argument('--num_jobs', type=int, default=5)
    args = parser.parse_args()
    result = scraper.scrape_jobs(args.job_title, args.location, args.num_jobs)
    print(json.dumps(result, indent=2)) 