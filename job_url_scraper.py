from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, Any
from jobspy import scrape_jobs

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "https://localhost:3000", "*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class JobUrlRequest(BaseModel):
    site_name: str
    search_term: Optional[str] = None
    google_search_term: Optional[str] = None
    location: Optional[str] = None
    results_wanted: Optional[int] = 5
    country_indeed: Optional[str] = "india"
    hours_old: Optional[int] = 72
    job_type: Optional[str] = None
    is_remote: Optional[bool] = None
    easy_apply: Optional[bool] = None
    description_format: Optional[str] = None
    offset: Optional[int] = 0
    verbose: Optional[int] = 2
    linkedin_fetch_description: Optional[bool] = None
    proxies: Optional[List[str]] = None


def run_url_scraper(params: dict) -> Any:
    try:
        jobs = scrape_jobs(**params)
        if jobs.empty:
            raise HTTPException(status_code=404, detail="No jobs found.")
        # Only extract the job URLs
        if 'job_url' not in jobs.columns:
            raise HTTPException(status_code=500, detail="job_url column not found in results.")
        url_list = jobs['job_url'].dropna().unique().tolist()
        return {"message": f"Found {len(url_list)} job URLs", "job_urls": url_list}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scrape-job-urls/")
async def scrape_job_urls(request: JobUrlRequest):
    params = request.dict(exclude_none=True)
    params["site_name"] = [params["site_name"]]
    return run_url_scraper(params)

@app.options("/scrape-job-urls/")
async def options_job_urls():
    return {"message": "OK"} 