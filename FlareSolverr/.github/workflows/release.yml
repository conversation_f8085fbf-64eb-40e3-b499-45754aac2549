name: release

on:
  push:
    tags:
      - 'v*.*.*'

jobs:
  create-release:
    name: Create release
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # get all commits, branches and tags (required for the changelog)

      - name: Build changelog
        id: github_changelog
        run: |
          changelog=$(git log $(git tag | tail -2 | head -1)..HEAD --no-merges --oneline)
          changelog="${changelog//'%'/'%25'}"
          changelog="${changelog//$'\n'/'%0A'}"
          changelog="${changelog//$'\r'/'%0D'}"
          echo "##[set-output name=changelog;]${changelog}"

      - name: Create release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          body: ${{ steps.github_changelog.outputs.changelog }}
          draft: false
          prerelease: false

  build-linux-package:
    name: Build Linux binary
    needs: create-release
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # get all commits, branches and tags (required for the changelog)

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Build artifacts
        run: |
          python -m pip install -r requirements.txt
          python -m pip install pyinstaller==5.13.0
          cd src
          python build_package.py

      - name: Upload release artifacts
        uses: alexellis/upload-assets@0.4.0
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
        with:
          asset_paths: '["./dist/flaresolverr_*"]'

  build-windows-package:
    name: Build Windows binary
    needs: create-release
    runs-on: windows-2022
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # get all commits, branches and tags (required for the changelog)

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Build artifacts
        run: |
          python -m pip install -r requirements.txt
          python -m pip install pyinstaller==5.13.0
          cd src
          python build_package.py

      - name: Upload release artifacts
        uses: alexellis/upload-assets@0.4.0
        env:
          GITHUB_TOKEN: ${{ secrets.GH_PAT }}
        with:
          asset_paths: '["./dist/flaresolverr_*"]'
