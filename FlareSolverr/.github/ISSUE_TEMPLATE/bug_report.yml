name: Bug report
description: Create a report of your issue
body:
- type: checkboxes
  attributes:
    label: Have you checked our README?
    description: Please check the <a href="https://github.com/FlareSolverr/FlareSolverr/blob/master/README.md">README</a>.
    options:
      - label: I have checked the README
        required: true
- type: checkboxes
  attributes:
    label: Have you followed our Troubleshooting?
    description: Please follow our <a href="https://github.com/FlareSolverr/FlareSolverr/wiki/Troubleshooting">Troubleshooting</a>.
    options:
      - label: I have followed your Troubleshooting
        required: true
- type: checkboxes
  attributes:
    label: Is there already an issue for your problem?
    description: Please make sure you are not creating an already submitted <a href="https://github.com/FlareSolverr/FlareSolverr/issues">Issue</a>. Check closed issues as well, because your issue may have already been fixed.
    options:
      - label: I have checked older issues, open and closed
        required: true
- type: checkboxes
  attributes:
    label: Have you checked the discussions?
    description: Please read our <a href="https://github.com/FlareSolverr/FlareSolverr/discussions">Discussions</a> before submitting your issue, some wider problems may be dealt with there.
    options:
      - label: I have read the Discussions
        required: true
- type: input
  attributes:
    label: Have you ACTUALLY checked all these?
    description: Please do not waste our time and yours; these checks are there for a reason, it is not just so you can tick boxes for fun. If you type <b>YES</b> and it is clear you did not or have put in no effort, your issue will be closed and locked without comment. If you type <b>NO</b> but still open this issue, you will be permanently blocked for timewasting.
    placeholder: YES or NO
  validations:
    required: true
- type: textarea
  attributes:
    label: Environment
    description: Please provide the details of the system FlareSolverr is running on.
    value: |
      - FlareSolverr version:
      - Last working FlareSolverr version:
      - Operating system:
      - Are you using Docker: [yes/no]
      - FlareSolverr User-Agent (see log traces or / endpoint):
      - Are you using a VPN: [yes/no]
      - Are you using a Proxy: [yes/no]
      - Are you using Captcha Solver: [yes/no]
      - If using captcha solver, which one:
      - URL to test this issue:
    render: markdown
  validations:
    required: true
- type: textarea
  attributes:
    label: Description
    description: List steps to reproduce the error and details on what happens and what you expected to happen.
  validations:
    required: true
- type: textarea
  attributes:
    label: Logged Error Messages
    description: |
      Place any relevant error messages you noticed from the logs here.
      Make sure you attach the full logs with your personal information removed in case we need more information.
      If you wish to provide debug logs, follow the instructions from this <a href="https://github.com/FlareSolverr/FlareSolverr/wiki/How-to-enable-debug-and-html-trace">wiki page</a>.
    render: text
  validations:
    required: true
- type: textarea
  attributes:
    label: Screenshots
    description: Place any screenshots of the issue here if needed
  validations:
    required: false
