import sys
import time
import json
import logging
import threading
import random
from typing import List, Dict, Optional
from contextlib import contextmanager
from queue import Queue, Empty
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException
)
from fastapi import FastAPI, Query, HTTPException, Body
from fastapi.responses import J<PERSON>NResponse
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import concurrent.futures

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Foundit Job Scraper", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://5969-202-148-58-231.ngrok-free.app/",  # frontend ngrok URL
        "http://localhost:3000",
        "https://localhost:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# CRITICAL FIX 1: Improved Driver Pool with Proper Resource Management
class ImprovedDriverPool:
    def __init__(self, size, setup_driver_func):
        self.pool = Queue()
        self.size = size
        self.setup_driver_func = setup_driver_func
        self.lock = threading.Lock()
        self.active_drivers = set()
        self.failed_drivers = set()
        
        # Initialize pool
        for _ in range(size):
            try:
                driver = setup_driver_func()
                self.pool.put(driver)
                self.active_drivers.add(id(driver))
            except Exception as e:
                logger.error(f"Failed to create driver during pool initialization: {e}")
    
    @contextmanager
    def get_driver(self, timeout=30):
        """Context manager for safe driver usage"""
        driver = None
        try:
            driver = self.pool.get(timeout=timeout)
            yield driver
        except Empty:
            logger.error("No available drivers in pool")
            raise Exception("Driver pool exhausted")
        except Exception as e:
            logger.error(f"Error using driver: {e}")
            if driver:
                self.failed_drivers.add(id(driver))
            raise
        finally:
            if driver:
                if id(driver) not in self.failed_drivers:
                    self.pool.put(driver)
                else:
                    self._replace_failed_driver(driver)
    
    def _replace_failed_driver(self, failed_driver):
        """Replace a failed driver with a new one"""
        with self.lock:
            try:
                failed_driver.quit()
            except Exception:
                pass
            
            try:
                new_driver = self.setup_driver_func()
                self.pool.put(new_driver)
                self.active_drivers.add(id(new_driver))
                logger.info("Replaced failed driver with new one")
            except Exception as e:
                logger.error(f"Failed to replace driver: {e}")
    
    def close_all(self):
        """Safely close all drivers"""
        with self.lock:
            while not self.pool.empty():
                try:
                    driver = self.pool.get_nowait()
                    driver.quit()
                except Exception as e:
                    logger.error(f"Error closing driver: {e}")
            self.active_drivers.clear()
            self.failed_drivers.clear()

class FounditScraper:
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.driver = None
        self.wait = None
        
    # CRITICAL FIX 2: Enhanced Setup Driver with Anti-Detection
    def setup_driver_with_anti_detection(self) -> webdriver.Chrome:
        """Enhanced driver setup with better anti-detection and performance optimizations"""
        options = Options()
        
        # Randomize user agents
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        if self.headless:
            options.add_argument('--headless=new')
        
        options.add_argument(f'--user-agent={random.choice(user_agents)}')
        
        # Enhanced anti-detection and performance
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')
        options.add_argument('--disable-javascript')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--disable-features=VizDisplayCompositor')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Randomize window size
        window_sizes = ['1920,1080', '1366,768', '1536,864', '1440,900']
        options.add_argument(f'--window-size={random.choice(window_sizes)}')
        
        # Performance optimizations
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.managed_default_content_settings.stylesheets": 2,
            "profile.managed_default_content_settings.fonts": 2,
            "profile.managed_default_content_settings.plugins": 2,
            "profile.managed_default_content_settings.popups": 2,
            "profile.managed_default_content_settings.geolocation": 2,
            "profile.managed_default_content_settings.notifications": 2,
            "profile.managed_default_content_settings.media_stream": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.media_stream_mic": 2,
            "profile.managed_default_content_settings.media_stream_camera": 2,
        }
        options.add_experimental_option("prefs", prefs)
        
        try:
            service = Service('./chromedriver')
            driver = webdriver.Chrome(service=service, options=options)
            
            # Additional stealth measures
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.execute_cdp_cmd('Network.setUserAgentOverride', {
                "userAgent": random.choice(user_agents)
            })
            
            # Set timeouts
            driver.set_page_load_timeout(20)
            driver.set_script_timeout(15)
            
            return driver
        except Exception as e:
            logger.error(f"Failed to setup driver: {e}")
            raise

    def setup_driver(self) -> webdriver.Chrome:
        """Legacy method for backward compatibility"""
        return self.setup_driver_with_anti_detection()

    # CRITICAL FIX 4: Enhanced Cookie Banner Handling
    def enhanced_cookie_banner_handling(self):
        """Enhanced cookie banner handling with timeout protection"""
        try:
            # Quick cookie banner check with timeout
            WebDriverWait(self.driver, 2).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, "#acceptAll"))
            ).click()
            logger.info("Cookie banner clicked: #acceptAll")
            time.sleep(0.5)
            return True
        except Exception:
            # Try alternative selectors quickly
            cookie_selectors = [
                "#cookieBanner #acceptAll",
                ".cookie-accept",
                "button[onclick*='accept']"
            ]
            
            for selector in cookie_selectors:
                try:
                    element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if element.is_displayed() and element.is_enabled():
                        element.click()
                        logger.info(f"Cookie banner clicked: {selector}")
                        time.sleep(0.3)
                        return True
                except Exception:
                    continue
        
        return False

    def handle_cookie_banner(self):
        """Legacy method for backward compatibility"""
        return self.enhanced_cookie_banner_handling()

    def safe_find_element(self, parent_element, selector: str, attribute: str = 'text') -> Optional[str]:
        try:
            element = parent_element.find_element(By.CSS_SELECTOR, selector)
            if attribute == 'text':
                return element.text.strip()
            else:
                return element.get_attribute(attribute)
        except NoSuchElementException:
            return None

    def extract_job_ids(self):
        """Extract job IDs from cardContainer divs"""
        job_ids = []
        try:
            # Find all divs with class containing 'cardContainer' (handles both 'cardContainer' and 'cardContainer activeCard')
            cards = self.driver.find_elements(By.CSS_SELECTOR, 'div[class*="cardContainer"]')
            logger.info(f"Found {len(cards)} card containers")
            for i, card in enumerate(cards):
                try:
                    job_id = card.get_attribute('id')
                    if job_id and job_id.isdigit() and len(job_id) >= 6:  # Ensure it's a valid job ID
                        job_ids.append(job_id)
                        logger.info(f"Card {i+1}: Found job ID: {job_id}")
                    else:
                        logger.warning(f"Card {i+1}: Invalid or missing job ID: {job_id}")
                except Exception as e:
                    logger.warning(f"Card {i+1}: Error extracting job ID: {e}")
        except Exception as e:
            logger.error(f"Error finding card containers: {e}")
        # Remove duplicates while preserving order
        unique_job_ids = []
        seen = set()
        for job_id in job_ids:
            if job_id not in seen:
                unique_job_ids.append(job_id)
                seen.add(job_id)
            else:
                logger.warning(f"Duplicate job ID found: {job_id}")
        logger.info(f"Total unique job IDs found: {len(unique_job_ids)} - {unique_job_ids}")
        return unique_job_ids

    def _go_to_next_page_safe(self, current_page: int) -> bool:
        """Safely navigate to next page"""
        try:
            self.handle_cookie_banner()
            
            # Look for next page button
            next_button = self.driver.find_element(By.CSS_SELECTOR, '.arrow.arrow-right:not(.disabled)')
            if not next_button or not next_button.is_enabled():
                return False
            
            next_button.click()
            self._random_delay(2, 4)
            
            # Wait for page change
            WebDriverWait(self.driver, 10).until(
                lambda d: int(d.find_element(By.CSS_SELECTOR, '.number.activePage').text) == current_page + 1
            )
            
            return True
            
        except Exception as e:
            logger.warning(f"Failed to navigate to next page: {e}")
            return False

    def go_to_next_page(self, current_page: int) -> bool:
        """Legacy method for backward compatibility"""
        return self._go_to_next_page_safe(current_page)

    def _random_delay(self, min_seconds: float, max_seconds: float):
        """Add random delay to avoid detection"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)

    def _perform_search(self, job_title: str, location: str) -> bool:
        """Safely perform search with retries"""
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                homepage_url = "https://www.foundit.in/"
                logger.info(f"Loading homepage (attempt {attempt + 1})")
                
                self.driver.get(homepage_url)
                self._random_delay(1, 3)
                
                self.handle_cookie_banner()
                
                search_url = (
                    f"https://www.foundit.in/srp/results?"
                    f"query={job_title.replace(' ', '+')}&"
                    f"location={location.replace(' ', '+')}"
                )
                
                logger.info(f"Loading search URL: {search_url}")
                self.driver.get(search_url)
                self._random_delay(2, 4)
                
                # Wait for job cards
                try:
                    WebDriverWait(self.driver, 15).until(
                        EC.presence_of_all_elements_located((By.CSS_SELECTOR, 'div.cardContainer'))
                    )
                    logger.info("Job cards loaded successfully")
                    return True
                except TimeoutException:
                    logger.warning(f"Timeout waiting for job cards (attempt {attempt + 1})")
                    continue
                    
            except Exception as e:
                logger.error(f"Search attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    self._random_delay(5, 10)
                continue
        
        return False

    def _collect_job_ids_safely(self, num_jobs: int) -> List[str]:
        """Safely collect job IDs with error handling"""
        all_job_ids = []
        current_page = 1
        max_pages = 10  # Limit to prevent infinite loops
        
        while len(all_job_ids) < num_jobs and current_page <= max_pages:
            try:
                self.handle_cookie_banner()
                self._random_delay(1, 2)
                
                page_job_ids = self.extract_job_ids()
                
                for job_id in page_job_ids:
                    if job_id not in all_job_ids and len(all_job_ids) < num_jobs:
                        all_job_ids.append(job_id)
                
                logger.info(f"Page {current_page}: Found {len(page_job_ids)} job IDs, Total: {len(all_job_ids)}")
                
                if len(all_job_ids) >= num_jobs:
                    break
                
                # Try to go to next page
                if not self._go_to_next_page_safe(current_page):
                    logger.info("No more pages available")
                    break
                
                current_page += 1
                
            except Exception as e:
                logger.error(f"Error collecting job IDs on page {current_page}: {e}")
                break
        
        return all_job_ids

    def _process_jobs_parallel_safe(self, job_ids: List[str], all_available_job_ids: List[str] = None) -> List[Dict]:
        """Process jobs in parallel with fallback to alternative job IDs"""
        if not job_ids:
            return []
        
        detail_urls = [f"https://www.foundit.in/seeker/job-details?id={job_id}" for job_id in job_ids]
        
        # Reduced pool size to prevent resource exhaustion
        if len(detail_urls) <= 5:
            pool_size = min(2, len(detail_urls))
        elif len(detail_urls) <= 10:
            pool_size = min(3, len(detail_urls))
        else:
            pool_size = min(4, len(detail_urls))  # Reduced from 6
        
        driver_pool = ImprovedDriverPool(pool_size, self.setup_driver_with_anti_detection)
        
        def safe_job_worker(url):
            """Worker function with comprehensive error handling"""
            try:
                with driver_pool.get_driver(timeout=30) as driver:  # Reduced timeout
                    return self._fetch_job_detail_safe(url, driver)
            except Exception as e:
                logger.error(f"Worker error for {url}: {e}")
                return None
        
        jobs = []
        failed_urls = []
        failed_job_ids = []
        
        try:
            # Reduced timeouts to prevent hanging
            per_job_timeout = 60  # Reduced from 90
            total_timeout = max(300, len(detail_urls) * 20)  # Reduced dynamic timeout
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=pool_size) as executor:
                # Submit all jobs
                future_to_url = {executor.submit(safe_job_worker, url): url for url in detail_urls}
                
                # Process results with shorter timeout
                completed_count = 0
                for future in concurrent.futures.as_completed(future_to_url, timeout=total_timeout):
                    url = future_to_url[future]
                    try:
                        result = future.result(timeout=per_job_timeout)
                        completed_count += 1
                        
                        if result:
                            jobs.append(result)
                            logger.info(f"Successfully scraped job {completed_count}/{len(detail_urls)} from {url}")
                        else:
                            failed_urls.append(url)
                            # Extract job ID from failed URL
                            job_id = url.split('id=')[-1]
                            failed_job_ids.append(job_id)
                            logger.warning(f"No data returned for {url}")
                            
                    except concurrent.futures.TimeoutError:
                        failed_urls.append(url)
                        job_id = url.split('id=')[-1]
                        failed_job_ids.append(job_id)
                        logger.error(f"Timeout error for {url}")
                    except Exception as e:
                        failed_urls.append(url)
                        job_id = url.split('id=')[-1]
                        failed_job_ids.append(job_id)
                        logger.error(f"Future error for {url}: {e}")
                        
        except concurrent.futures.TimeoutError:
            logger.error(f"Overall timeout reached. Processed {len(jobs)} jobs successfully")
            # Cancel remaining futures
            for future in future_to_url:
                future.cancel()
        except Exception as e:
            logger.error(f"Parallel processing error: {e}")
        
        # Try fallback job IDs if we have failed jobs and alternative IDs available
        if failed_job_ids and all_available_job_ids:
            fallback_jobs = self._try_fallback_job_ids(failed_job_ids, job_ids, all_available_job_ids, driver_pool)
            jobs.extend(fallback_jobs)
        
        # Close driver pool after all processing is complete
        driver_pool.close_all()
        
        if failed_urls:
            logger.warning(f"Failed to scrape {len(failed_urls)} jobs: {failed_urls}")
        
        return jobs

    def _try_fallback_job_ids(self, failed_job_ids: List[str], original_job_ids: List[str], all_available_job_ids: List[str], driver_pool) -> List[Dict]:
        """Try alternative job IDs for failed jobs"""
        fallback_jobs = []
        
        # Find alternative job IDs that weren't in the original request
        original_job_set = set(original_job_ids)  # The original job IDs we tried
        alternative_job_ids = [jid for jid in all_available_job_ids if jid not in original_job_set]
        
        if not alternative_job_ids:
            logger.warning("No alternative job IDs available for fallback")
            return fallback_jobs
        
        logger.info(f"Trying {len(alternative_job_ids)} alternative job IDs for {len(failed_job_ids)} failed jobs")
        
        # Try alternative jobs one by one to avoid overwhelming the system
        for i, failed_job_id in enumerate(failed_job_ids):
            if i < len(alternative_job_ids):
                alternative_job_id = alternative_job_ids[i]
                fallback_url = f"https://www.foundit.in/seeker/job-details?id={alternative_job_id}"
                
                try:
                    with driver_pool.get_driver(timeout=30) as driver:
                        result = self._fetch_job_detail_safe(fallback_url, driver)
                        if result:
                            fallback_jobs.append(result)
                            logger.info(f"Fallback successful: {failed_job_id} -> {alternative_job_id}")
                        else:
                            logger.warning(f"Fallback failed for {failed_job_id} -> {alternative_job_id}")
                except Exception as e:
                    logger.error(f"Fallback error for {failed_job_id} -> {alternative_job_id}: {e}")
        
        return fallback_jobs

    def _fetch_job_detail_safe(self, url: str, driver) -> Optional[Dict]:
        """Safely fetch job details with improved timeout handling and better error recovery"""
        max_retries = 3  # Increased retries
        
        for attempt in range(max_retries):
            try:
                # Set shorter page load timeout to prevent hanging
                driver.set_page_load_timeout(15)  # Further reduced
                driver.set_script_timeout(10)     # Further reduced
                
                # Clear browser cache and cookies for fresh start
                if attempt > 0:
                    driver.delete_all_cookies()
                    driver.execute_script("window.localStorage.clear();")
                    driver.execute_script("window.sessionStorage.clear();")
                    # Force garbage collection
                    driver.execute_script("if (window.gc) { window.gc(); }")
                
                driver.get(url)
                
                # Minimal delay for faster processing
                self._random_delay(0.2, 0.5)  # Further reduced
                
                # Handle cookie banner quickly with timeout
                try:
                    WebDriverWait(driver, 0.5).until(  # Reduced timeout
                        lambda d: d.find_element(By.CSS_SELECTOR, "#acceptAll")
                    ).click()
                    time.sleep(0.1)  # Minimal wait
                except Exception:
                    pass  # Don't let cookie handling block the process
                
                # Wait for main job content instead of full page load
                try:
                    WebDriverWait(driver, 8).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, ".job-tittle.detail-job-tittle h1"))
                    )
                except TimeoutException:
                    # If main content doesn't appear, try to extract data anyway
                    logger.warning(f"Job title element timeout for {url}, attempting to extract data")
                
                # Extract job data with timeout protection
                job_data = self._extract_job_data_safe(driver, url)
                
                # Validate required fields - be more lenient
                if self._validate_job_data(job_data):
                    return job_data
                elif job_data.get('title'):  # Accept if at least title is present
                    logger.warning(f"Partial job data for {url}, returning with title only")
                    return job_data
                else:
                    logger.warning(f"Invalid job data for {url}, attempt {attempt + 1}")
                    if attempt < max_retries - 1:
                        self._random_delay(0.3, 0.8)  # Minimal retry delay
                    continue
                    
            except TimeoutException as e:
                logger.error(f"Timeout error for {url} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    self._random_delay(0.5, 1)
                continue
            except Exception as e:
                logger.error(f"Error fetching job detail for {url} (attempt {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    self._random_delay(0.5, 1)
                continue
        
        return None

    def _validate_job_data(self, job_data: Dict) -> bool:
        """Validate job data quality with more lenient requirements"""
        if not job_data:
            return False
        
        # Check for title (most important field)
        if not job_data.get('title') or job_data['title'].strip() == '':
            logger.warning("Missing title field")
            return False
        
        # Check for placeholder text
        title = job_data.get('title', '').lower()
        if any(placeholder in title for placeholder in ['job title will display', 'loading', 'placeholder']):
            logger.warning("Placeholder text detected in title")
            return False
        
        # Company name is nice to have but not required
        if not job_data.get('company_name') or job_data['company_name'].strip() == '':
            logger.warning("Missing company name, but continuing with partial data")
        
        return True

    # CRITICAL FIX 3: Improved Scrape Jobs with Better Error Handling
    def improved_scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict:
        """Improved scrape_jobs with fallback to alternative job IDs"""
        start_time = time.time()
        jobs = []
        
        try:
            # Setup main driver for pagination
            self.driver = self.setup_driver_with_anti_detection()
            self.wait = WebDriverWait(self.driver, 10)
            
            # Navigate and search
            if not self._perform_search(job_title, location):
                return {'scraped_jobs': [], 'error': 'Failed to perform search'}
            
            # Collect job IDs with error handling - get more than requested for fallback
            all_job_ids = self._collect_job_ids_safely(num_jobs * 2)  # Get 2x more for fallback
            
            if not all_job_ids:
                return {'scraped_jobs': [], 'error': 'No job IDs collected'}
            
            # Process jobs with improved parallel scraping and fallback
            jobs_to_process = all_job_ids[:num_jobs]
            logger.info(f"Processing {len(jobs_to_process)} jobs in parallel with fallback support")
            
            jobs = self._process_jobs_parallel_safe(jobs_to_process, all_job_ids)
            
            elapsed = time.time() - start_time
            
            return {
                'scraped_jobs': jobs,
                'total_found': len(all_job_ids),
                'scraped_count': len(jobs),
                'failed_count': len(jobs_to_process) - len(jobs),
                'elapsed_seconds': round(elapsed, 2),
                'processed_job_ids': jobs_to_process,
                'fallback_used': len(jobs) > len(jobs_to_process)
            }
            
        except Exception as e:
            logger.error(f"Critical error in scrape_jobs: {e}")
            return {'scraped_jobs': jobs, 'error': str(e)}
        finally:
            if hasattr(self, 'driver') and self.driver:
                try:
                    self.driver.quit()
                except Exception:
                    pass

    def scrape_jobs(self, job_title: str, location: str, num_jobs: int = 5) -> Dict:
        """Legacy method for backward compatibility"""
        return self.improved_scrape_jobs(job_title, location, num_jobs)

    # CRITICAL FIX 5: Improved Data Extraction with Validation
    def _extract_job_data_safe(self, driver, url: str) -> Dict:
        """Safely extract job data with validation and timeout protection"""
        job_data = {
            'job_id': None,
            'title': None,
            'company_name': None,
            'locations': [],
            'experience': None,
            'salary': None,
            'posted_time': None,
            'job_description': None,
            'function': None,
            'roles': None,
            'skills': [],
            'job_type': None,
            'about_company': None,
            'detail_url': url
        }
        
        try:
            # Extract job ID
            job_data['job_id'] = self._extract_job_id_safe(url, driver)
            
            # Extract title with multiple strategies (most important field)
            job_data['title'] = self._extract_title_safe(driver, url)
            
            # Extract company name
            job_data['company_name'] = self._extract_company_name_safe(driver)
            
            # Extract other fields with timeout protection
            try:
                job_data['locations'] = self._extract_locations_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['experience'] = self._extract_experience_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['salary'] = self._extract_salary_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['posted_time'] = self._extract_posted_time_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['job_description'] = self._extract_job_description_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['function'] = self._extract_function_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['roles'] = self._extract_roles_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['skills'] = self._extract_skills_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['job_type'] = self._extract_job_type_safe(driver)
            except Exception:
                pass
                
            try:
                job_data['about_company'] = self._extract_about_company_safe(driver)
            except Exception:
                pass
            
        except Exception as e:
            logger.error(f"Error extracting job data from {url}: {e}")
        
        return job_data

    def _extract_title_safe(self, driver, url: str) -> Optional[str]:
        """Safely extract job title with multiple strategies"""
        title_strategies = [
            # Strategy 1: Primary selectors
            ['.job-tittle.detail-job-tittle h1', '.job-tittle.detail-job-tittle > h1'],
            # Strategy 2: Alternative selectors
            ['.main-heading.mt15.mb5.fs20', '.job-title h1', '.job-details-title h1'],
            # Strategy 3: Generic selectors
            ['h1[class*="job"]', 'h1[class*="title"]', '.jobTitle h1'],
            # Strategy 4: Fallback to any h1
            ['h1']
        ]
        
        for strategy in title_strategies:
            for selector in strategy:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and len(text) > 3:
                            # Skip common non-job-title text
                            skip_texts = ['job search', 'career', 'login', 'register', 'about us', 'contact']
                            if not any(skip in text.lower() for skip in skip_texts):
                                # Check for placeholder text
                                if "job title will display" not in text.lower():
                                    logger.info(f"Found title using selector '{selector}': {text}")
                                    return text
                except Exception as e:
                    logger.debug(f"Title selector '{selector}' failed: {e}")
                    continue
        
        # Try iframe extraction
        try:
            iframes = driver.find_elements(By.TAG_NAME, 'iframe')
            for iframe in iframes:
                try:
                    driver.switch_to.frame(iframe)
                    for strategy in title_strategies:
                        for selector in strategy:
                            try:
                                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                                for element in elements:
                                    text = element.text.strip()
                                    if text and len(text) > 3 and "job title will display" not in text.lower():
                                        driver.switch_to.default_content()
                                        return text
                            except Exception:
                                continue
                    driver.switch_to.default_content()
                except Exception:
                    driver.switch_to.default_content()
                    continue
        except Exception:
            pass
        
        logger.warning(f"No valid job title found for {url}")
        return None

    def _extract_company_name_safe(self, driver) -> Optional[str]:
        """Safely extract company name"""
        company_selectors = [
            '.color-secondary.medium.mt10 a',
            '.company-name a',
            '.company-name',
            '.company-info a',
            'a[href*="company"]'
        ]
        
        for selector in company_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text and len(text) > 1:
                    return text
            except Exception:
                continue
        
        return None

    def _extract_locations_safe(self, driver) -> List[str]:
        """Safely extract job locations"""
        location_selectors = [
            '.loc a',
            '.location a',
            '.job-location a',
            '.location-info a'
        ]
        
        for selector in location_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                locations = [el.text.strip() for el in elements if el.text.strip()]
                if locations:
                    return locations
            except Exception:
                continue
        
        return []

    def _extract_experience_safe(self, driver) -> Optional[str]:
        """Safely extract experience requirement"""
        exp_selectors = [
            '.exp',
            '.experience',
            '.job-experience',
            '.experience-required'
        ]
        
        for selector in exp_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text:
                    return text
            except Exception:
                continue
        
        return None

    def _extract_salary_safe(self, driver) -> Optional[str]:
        """Safely extract salary information"""
        salary_selectors = [
            '.package',
            '.salary',
            '.job-salary',
            '.compensation'
        ]
        
        for selector in salary_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text:
                    return text
            except Exception:
                continue
        
        return None

    def _extract_job_description_safe(self, driver) -> Optional[str]:
        """Safely extract job description"""
        desc_selectors = [
            '.job-description-content .jd-text',
            '.job-description-content',
            '.job-description',
            '.description-content'
        ]
        
        for selector in desc_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Try to get paragraph text first
                    paragraphs = elements[0].find_elements(By.TAG_NAME, 'p')
                    if paragraphs:
                        desc_parts = [p.text.strip() for p in paragraphs if p.text.strip()]
                        if desc_parts:
                            return '\n'.join(desc_parts)
                    
                    # Fallback to element text
                    text = elements[0].text.strip()
                    if text:
                        return text
            except Exception:
                continue
        
        return None

    def _extract_job_id_safe(self, url: str, driver) -> Optional[str]:
        """Safely extract job ID"""
        import re
        # Try to extract from URL first
        m = re.search(r'id=(\d+)', url)
        if m:
            return m.group(1)
        
        # Try to extract from page content
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, '.posted.pLR-10')
            for el in elements:
                text = el.text.strip()
                if 'Job Id:' in text:
                    return text.replace('Job Id:', '').strip()
        except Exception:
            pass
        
        return None

    def _extract_posted_time_safe(self, driver) -> Optional[str]:
        """Safely extract posted time"""
        try:
            elements = driver.find_elements(By.CSS_SELECTOR, '.posted.seprator.pLR-10')
            for el in elements:
                text = el.text.strip()
                if 'Posted On:' in text:
                    return text.replace('Posted On:', '').strip()
        except Exception:
            pass
        return None

    def _extract_function_safe(self, driver) -> Optional[str]:
        """Safely extract job function"""
        try:
            details = driver.find_elements(By.CSS_SELECTOR, '.job-detail-list')
            for detail in details:
                try:
                    h3 = detail.find_element(By.CSS_SELECTOR, '.dt-heading h3')
                    if h3 and 'Function:' in h3.text:
                        content = detail.find_element(By.CSS_SELECTOR, '.dt-content')
                        if content:
                            return content.text.strip()
                except Exception:
                    continue
        except Exception:
            pass
        return None

    def _extract_roles_safe(self, driver) -> Optional[str]:
        """Safely extract job roles"""
        try:
            details = driver.find_elements(By.CSS_SELECTOR, '.job-detail-list')
            for detail in details:
                try:
                    h3 = detail.find_element(By.CSS_SELECTOR, '.dt-heading h3')
                    if h3 and 'Roles:' in h3.text:
                        content = detail.find_element(By.CSS_SELECTOR, '.dt-content')
                        if content:
                            return content.text.strip()
                except Exception:
                    continue
        except Exception:
            pass
        return None

    def _extract_skills_safe(self, driver) -> List[str]:
        """Safely extract job skills"""
        skill_selectors = [
            '.job-detail-list .dt-heading h3:contains("Skills:") + .dt-content a',
            '.dt-content .round-card a',
            '.skills a',
            '.job-skills a'
        ]
        
        for selector in skill_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                skills = [el.text.strip() for el in elements if el.text.strip()]
                if skills:
                    return skills
            except Exception:
                continue
        
        return []

    def _extract_job_type_safe(self, driver) -> Optional[str]:
        """Safely extract job type"""
        type_selectors = [
            '.color-grey-light',
            '.job-type',
            '.employment-type',
            '.job-category'
        ]
        
        for selector in type_selectors:
            try:
                element = driver.find_element(By.CSS_SELECTOR, selector)
                text = element.text.strip()
                if text:
                    return text
            except Exception:
                continue
        
        return None

    def _extract_about_company_safe(self, driver) -> Optional[str]:
        """Safely extract about company information"""
        try:
            h2s = driver.find_elements(By.CSS_SELECTOR, 'h2')
            for h2 in h2s:
                if 'About' in h2.text:
                    parent = h2.find_element(By.XPATH, './ancestor::div[contains(@class, "card-body")]')
                    if parent:
                        return parent.text.strip()
        except Exception:
            pass
        return None

    # Legacy methods for backward compatibility
    def extract_title_from_context(self, context, url):
        """Legacy method for backward compatibility"""
        return self._extract_title_safe(context, url)

    def fetch_and_parse_job_detail_selenium(self, url, max_retries=2, driver=None):
        """Legacy method for backward compatibility"""
        if driver:
            return self._fetch_job_detail_safe(url, driver)
        else:
            return self._fetch_job_detail_safe(url, self.setup_driver_with_anti_detection())

    def find_first_non_empty_text(self, context, selector):
        """Legacy method for backward compatibility"""
        elements = context.find_elements(By.CSS_SELECTOR, selector)
        for el in elements:
            text = el.text.strip()
            if text:
                return text
        return None

    def extract_all_texts_selenium(self, driver, selectors):
        """Legacy method for backward compatibility"""
        for selector in selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                texts = [el.text.strip() for el in elements if el.text.strip()]
                if texts:
                    return texts
            except Exception:
                continue
        return []

    def extract_posted_time_selenium(self, driver):
        """Legacy method for backward compatibility"""
        return self._extract_posted_time_safe(driver)

    def extract_job_id_from_detail_url_selenium(self, url, driver):
        """Legacy method for backward compatibility"""
        return self._extract_job_id_safe(url, driver)

    def extract_job_description_detail_selenium(self, driver):
        """Legacy method for backward compatibility"""
        return self._extract_job_description_safe(driver)

    def extract_job_detail_list_selenium(self, driver, heading):
        """Legacy method for backward compatibility"""
        if heading == 'Function:':
            return self._extract_function_safe(driver)
        elif heading == 'Roles:':
            return self._extract_roles_safe(driver)
        return None

    def extract_about_company_detail_selenium(self, driver):
        """Legacy method for backward compatibility"""
        return self._extract_about_company_safe(driver)

class FounditRequest(BaseModel):
    job_title: str
    location: str
    num_jobs: int = 5

scraper = FounditScraper(headless=False)

@app.get("/scrape_foundit")
async def scrape_foundit_api(
    job_title: str = Query(..., description="Job title to search for"),
    location: str = Query(..., description="Location to search in"),
    num_jobs: int = Query(5, ge=1, le=50, description="Number of jobs to scrape")
):
    try:
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/scrape_foundit")
async def scrape_foundit_post_api(request: FounditRequest):
    try:
        result = scraper.scrape_jobs(request.job_title, request.location, request.num_jobs)
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        return JSONResponse(content=result)
    except Exception as e:
        logger.error(f"API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.options("/scrape_foundit")
async def options_scrape_foundit():
    return {"message": "OK"}

@app.options("/scrape_foundit")
async def options_scrape_foundit_post():
    return {"message": "OK"}

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        uvicorn.run("foundit_scraper:app", host="0.0.0.0", port=8002, reload=True)
    else:
        job_title = sys.argv[1] if len(sys.argv) > 1 else "Software Engineer"
        location = sys.argv[2] if len(sys.argv) > 2 else "India"
        num_jobs = int(sys.argv[3]) if len(sys.argv) > 3 else 5
        scraper = FounditScraper(headless=False)
        result = scraper.scrape_jobs(job_title, location, num_jobs)
        print(json.dumps(result, indent=2))