# scraper-backend

# Job Scraper API Usage Guide

This guide shows how to use the POST scrape endpoints for each job scraper via `curl` and Thunder Client, assuming your backend is running at:

```
https://7c12-103-247-7-136.ngrok-free.app/
```

---

## 1. Foundit Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/foundit/scrape_foundit
- Body: JSON (see above)

---

## 2. Glassdoor Scraper (Parallel)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/glassdoor/scrape_jobs_parallel
- Body: JSON (see above)

---

## 3. SimplyHired Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5,
  "headless": true,
  "detailed_extraction": true
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true,
    "detailed_extraction": true
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/simplyhired/scrape_simplyhired
- Body: JSON (see above)

---

## 4. ZipRecruiter Scraper

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter
```

**Example JSON Body:**

```json
{
  "job_title": "Data Scientist",
  "location": "India",
  "num_jobs": 5,
  "headless": true
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter \
  -H "Content-Type: application/json" \
  -d '{
    "job_title": "Data Scientist",
    "location": "India",
    "num_jobs": 5,
    "headless": true
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/ziprecruiter/scrape_ziprecruiter
- Body: JSON (see above)

---

## 5. LinkedIn Scraper (Detailed, via main.py)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/
```

**Example JSON Body:**

```json
{
  "site_name": "linkedin",
  "search_term": "Data Scientist",
  "location": "India",
  "results_wanted": 5,
  "hours_old": 72,
  "job_type": null,
  "is_remote": null,
  "easy_apply": null,
  "offset": 0,
  "verbose": 2,
  "linkedin_fetch_description": true,
  "proxies": null
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/ \
  -H "Content-Type: application/json" \
  -d '{
    "site_name": "linkedin",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": true,
    "proxies": null
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/scrape-linkedin/
- Body: JSON (see above)

---

## 6. Indeed Scraper (via main.py)

**Endpoint:**

```
POST https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/
```

**Example JSON Body:**

```json
{
  "site_name": "indeed",
  "search_term": "Data Scientist",
  "location": "India",
  "results_wanted": 5,
  "country_indeed": "india",
  "hours_old": 72,
  "job_type": null,
  "is_remote": null,
  "easy_apply": null,
  "offset": 0,
  "verbose": 2,
  "linkedin_fetch_description": null,
  "proxies": null
}
```

**cURL Example:**

```sh
curl -X POST \
  https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/ \
  -H "Content-Type: application/json" \
  -d '{
    "site_name": "indeed",
    "search_term": "Data Scientist",
    "location": "India",
    "results_wanted": 5,
    "country_indeed": "india",
    "hours_old": 72,
    "job_type": null,
    "is_remote": null,
    "easy_apply": null,
    "offset": 0,
    "verbose": 2,
    "linkedin_fetch_description": null,
    "proxies": null
  }'
```

**Thunder Client:**

- Method: POST
- URL: https://7c12-103-247-7-136.ngrok-free.app/scrape-indeed/
- Body: JSON (see above)

---

## Notes

- All endpoints expect `Content-Type: application/json`.
- Adjust `job_title`, `location`, and other parameters as needed.
- For Thunder Client, paste the JSON body in the "Body" tab and select "JSON".
- For more details on each scraper's response, see the respective Python file.
